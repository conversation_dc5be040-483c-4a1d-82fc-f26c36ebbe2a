'use client';

import {
  useCallback,
  useState,
  useRef,
  useLayoutEffect,
  useEffect,
} from 'react';
import { Alert, Box, Snackbar } from '@mui/material';
import axios from 'axios';
import FilterRoomsSection from '@/components/new-version/FilterRoomsSection';
import RoomsSelectionList from '@/components/new-version/RoomsSelection';
import FacilitiesAndAmenities from '@/components/new-version/FacilitiesAndAmenities';
import GalleryImages from '@/components/new-version/Gallery/GalleryImages';
import CapsuleTransitHeading from '@/components/new-version/CapsuleTransitHeading';
import GuideKliaT2 from '@/components/new-version/GuideKliaT2';
import useGetRooms from './hooks/useGetRooms';
import SelectedRoom from '@/components/new-version/SelectedRoom';
import NavbarNewBooking from '@/components/new-version/NavbarNewBooking';
import { FilterData } from '@/components/new-version/FilterRoomsSection/types';
import { OUTLETS, PriceOption } from '@/constant/outlet';
import BookingPayment from '@/components/new-version/BookingPayment';
import useGetHotelDetail from '@/hooks/useGetHotelDetail';
import useGetTaxAndServiceChargeByLot from '@/hooks/useGetTaxAndServiceChargeByLot';
import useGetGuestsCountry from '@/hooks/useGetGuestsCountry';
import { FormPaymentData } from './types/form-payment-data';
import { API_BASE_URL } from '@/config/app';
import { useBookingData } from '@/context/BookingContext';
import { IBookingInformation } from '@/models/Booking';
import { DURATIONS } from '@/constant/time-selection';
import Footer from '@/components/new-version/Footer';
import { RoomsSelectionProvider } from '@/components/new-version/RoomsSelection/context';
import { SelectedRooms } from '@/components/new-version/RoomsSelection/types';
import { RoomImagesApiResponse } from '@/actions/getRoomImages/types';

const BookingPageClient = ({
  roomImageData,
}: {
    roomImageData: RoomImagesApiResponse;
}) => {
  const { setBookingData } = useBookingData();
  const [errorNotification, setErrorNotification] = useState<string>('');

  const handleOpenErrorBooking = (text: string) => {
    setErrorNotification(text);
  };

  const handleCloseErrorBooking = () => {
    setErrorNotification('');
  };
  const [selectedRooms, setSelectedRooms] = useState<SelectedRooms>({});
  const [filters, setFilters] = useState<FilterData>({});
  const roomsSelectionRef = useRef<HTMLDivElement>(null);

  const [isScrollingToRoomsSelectionList, setIsScrollingToRoomsSelectionList] =
    useState(false);
  const [isFirstLoad, setIsFirstLoad] = useState(true);

  // Function to check if rooms-selection-list is visible in viewport
  const isRoomsSelectionVisible = useCallback(() => {
    const targetElement = document.getElementById('rooms-selection-list');
    if (!targetElement) return false;

    const rect = targetElement.getBoundingClientRect();
    const windowHeight = window.innerHeight || document.documentElement.clientHeight;

    // Check if at least 50% of the element is visible
    const visibleHeight = Math.min(rect.bottom, windowHeight) - Math.max(rect.top, 0);
    const elementHeight = rect.height;

    return visibleHeight > 0 && (visibleHeight / elementHeight) >= 0.5;
  }, []);

  const onApplyFilter = useCallback((filterData: FilterData) => {
    setFilters(filterData);
    // reset selected Rooms when filters changed
    setSelectedRooms({});

    // Only scroll if this is first load or rooms-selection-list is not currently visible
    if (isFirstLoad || !isRoomsSelectionVisible()) {
      setIsScrollingToRoomsSelectionList(true);
    }

    // Mark that first load is complete
    if (isFirstLoad) {
      setIsFirstLoad(false);
    }
  }, [isFirstLoad, isRoomsSelectionVisible]);

  useLayoutEffect(() => {
    if (!isScrollingToRoomsSelectionList) return;
    // Smooth scroll to RoomsSelectionList after a short delay to ensure state update
    setTimeout(() => {
      const targetElement = document.getElementById('rooms-selection-list');
      if (targetElement) {
        // Get the filter element height to account for fixed positioning
        const filterElement = document.querySelector('[data-filter-section]');
        let filterHeight = filterElement
          ? filterElement.getBoundingClientRect().height
          : 0;

        const headerElement = document.querySelector('header');
        const headerHeight = headerElement
          ? headerElement.getBoundingClientRect().height
          : 0;

        filterHeight += headerHeight;

        // Calculate the target scroll position with offset for fixed filter
        const elementTop = targetElement.offsetTop;
        const offsetPosition = elementTop - filterHeight - 20; // 20px additional spacing

        window.scrollTo({
          top: Math.max(0, offsetPosition), // Ensure we don't scroll to negative position
          behavior: 'smooth',
        });
        setIsScrollingToRoomsSelectionList(false);
      }
    }, 200);
  }, [isScrollingToRoomsSelectionList]);

  // Handle initial scroll on first load when filters are available
  useEffect(() => {
    if (isFirstLoad && filters.lotId && !isScrollingToRoomsSelectionList) {
      // Delay to ensure the rooms-selection-list element is rendered
      setTimeout(() => {
        const targetElement = document.getElementById('rooms-selection-list');
        if (targetElement) {
          setIsScrollingToRoomsSelectionList(true);
        }
      }, 500);
    }
  }, [filters.lotId, isFirstLoad, isScrollingToRoomsSelectionList]);

  useEffect(() => {
    // get offset top of roomsSelectionRef.current when scrolling down fired
    if (roomsSelectionRef.current) {
      window.addEventListener('scroll', () => {
        const offsetTop = roomsSelectionRef.current?.offsetTop;
        console.log('offsetTop', offsetTop);
      });
    }
  }, [filters]);

  const { data: hotelDetail } = useGetHotelDetail({
    lotId: Number(filters.lotId),
  });

  const { data: taxAndServiceCharge } = useGetTaxAndServiceChargeByLot({
    lotId: Number(filters.lotId),
  });

  const { data: guestsCountry } = useGetGuestsCountry();

  const {
    rooms,
    isLoading: roomsLoading,
    roomPriceOptions: otherAvailableRoomOptions,
    isLoadingRoomsByDurations: isLoadingOtherAvailableRoomOptions,
  } = useGetRooms({
    checkInDatetime: filters.checkInDatetime || 0,
    duration: filters.stayDuration || 0,
    lotId: filters.lotId ? parseInt(filters.lotId) : 0,
    durations: DURATIONS.map((duration) => duration.value),
  });

  const handleSelectRoom = ({
    room,
    quantity,
  }: {
    room: any;
    quantity: number;
  }) => {
    setSelectedRooms((prev) => ({
      ...prev,
      [room.name]: {
        room,
        quantity,
      },
    }));
  };

  const handleRemoveRoom = (room: any) => {
    setSelectedRooms((prev) => {
      const rooms = { ...prev };

      delete rooms[room.name];

      return rooms;
    });
  };

  const handleChangeOtherAvailableOption = (
    room: any,
    selectedOption: PriceOption
  ) => {
    setFilters((prev) => ({
      ...prev,
      stayDuration: selectedOption.hours,
    }));
    // since changing price option, the selected room should be reset
    if (Object.keys(selectedRooms)) {
      setSelectedRooms((prev) => {
        const rooms = { ...prev };

        if (rooms[room.name]) {
          // change the duration of selected room
          return {
            [room.name]: {
              ...rooms[room.name],
              room: {
                ...room,
                selectedOption: selectedOption,
              },
            },
          };
        }

        // reset other selection after change duration
        return {};
      });
    }
  };

  // Transform selectedRooms to the format expected by SelectedRoom component
  const transformSelectedRooms = () => {
    const transformedRooms = Object.values(selectedRooms).map((item: any) => ({
      id: item.room.id,
      room: item.room,
      bedType: item.room.bedType,
      roomType: item.room.name,
      roomName: item.room.name,
      quantity: item.quantity,
      zone: item.room.zone,
      duration: item.room.selectedOption.hours || filters.stayDuration,
      price: item.room.selectedOption.price || item.room.price,
      imageUrl: item.room.images?.[0]?.url ? [item.room.images?.[0]?.url] : [],
    }));
    return transformedRooms;
  };

  const [clickedBookNow, setClickedBookNow] = useState<boolean>(false);

  const onHandlePayment = async (formData: FormPaymentData) => {
    try {
      const bodyData = {
        lotId: hotelDetail.lotId,
        checkinDatetime: filters.checkInDatetime,
        duration: filters.stayDuration,
        roomTypes: transformSelectedRooms().map((r) => ({
          roomTypeName: r.roomType,
          quantity: r.quantity,
          roomPrice: r.price,
          duration: r.duration,
        })),
        ...formData,
      };

      const result = await axios
        .post(`${API_BASE_URL}/landing-page/booking/`, bodyData, {
          headers: {
            'Content-Type': 'application/json',
            Accept: 'application/json',
          },
        })
        .then((res) => res.data.data || {});

      const bookingData = {
        guestDetail: formData,
        payment: formData,
        roomBookings: transformSelectedRooms(),
        selectedHotel: hotelDetail,
        bookingSchedule: filters,
        bookingNo: result.bookingNo,
        bookingId: result.bookingId,
      };

      setBookingData(bookingData as unknown as IBookingInformation);

      const formatter = new Intl.DateTimeFormat('en-US', {
        timeZone: 'Asia/Singapore',
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false, // Use 24-hour format
      });

      const singaporeTime = formatter.format(filters.date as Date);

      const iPay88Data = {
        amount: bodyData.creditAmount,
        refNo: result?.bookingId,
        bookingNo: `${result?.bookingNo}, Check In Date ${singaporeTime}`,
        userContact: bodyData.phoneNumber,
        userEmail: bodyData.email,
        userName: bodyData.firstName + ' ' + bodyData.lastName,
        lot: hotelDetail?.lotNumber,
      };

      const roomDescriptions = transformSelectedRooms()
        .map((r) => `${r.quantity}x ${r.roomType}`)
        .join(', ');

      const productDescription = `${iPay88Data.lot} Capsule Transit: ${roomDescriptions}`;

      const urlSearchParams = new URLSearchParams();
      urlSearchParams.append('refNo', iPay88Data.refNo);
      urlSearchParams.append('bookingNo', iPay88Data.bookingNo);
      urlSearchParams.append('amount', String(iPay88Data.amount));
      urlSearchParams.append('contact', iPay88Data.userContact);
      urlSearchParams.append('email', iPay88Data.userEmail);
      urlSearchParams.append('name', iPay88Data.userName);
      urlSearchParams.append('prodDesc', productDescription);
      urlSearchParams.append('lot', hotelDetail?.lotNumber);

      const checkoutUrl = `/new-booking/checkout?${urlSearchParams.toString()}`;

      return {
        success: true,
        data: {
          checkoutUrl,
          iPay88Data,
          ...result,
        },
      };
    } catch (error: any) {
      console.log('error', error);

      const errorMessage =
        error?.response?.data?.message || 'Something went wrong';

      handleOpenErrorBooking(errorMessage);

      return {
        success: false,
        error: error,
      };
    }
  };

  const outlet = OUTLETS.find((outlet) => outlet.lotId === Number(filters.lotId));

  return (
    <Box
      display={'flex'}
      width={'99vw'}
      flexDirection={'column'}
      sx={{
        overflowX: 'hidden',
        paddingTop: { xs: '76px', md: !clickedBookNow ? '230px' : '76px' },
      }}
    >
      <NavbarNewBooking
        onClose={() => {
          // go back to room selection
          setClickedBookNow(false);
        }}
      />
      <Box display={!clickedBookNow ? 'block' : 'none'}>
        <Box
          data-filter-section
          sx={{
            position: { xs: 'unset', md: 'fixed' },
            top: { xs: 'unset', md: '76px' },
            left: { xs: 'unset', md: '0' },
            right: { xs: 'unset', md: '0' },
            zIndex: 100,
          }}
        >
          <FilterRoomsSection onApplyFilter={onApplyFilter} />
        </Box>
        {filters.lotId && (
          <CapsuleTransitHeading lotId={Number(filters.lotId)} />
        )}
        <GalleryImages
          roomImageData={roomImageData}
          outlet={outlet as { key: string; lotId: number } & { [k: string]: any }}
        />
        <FacilitiesAndAmenities
          sx={{
            backgroundColor: '#fff',
          }}
        />
        <div ref={roomsSelectionRef} id='rooms-selection-list'>
          <RoomsSelectionProvider
            selectedRooms={selectedRooms}
            selectedDuration={filters.stayDuration}
            isLoadingOtherAvailableRoomOptions={
              isLoadingOtherAvailableRoomOptions
            }
            handleChangeOtherAvailableOption={handleChangeOtherAvailableOption}
            handleSelectRoom={handleSelectRoom}
            handleRemoveRoom={handleRemoveRoom}
            roomImageData={roomImageData}
          >
            <RoomsSelectionList
              rooms={rooms}
              isLoading={roomsLoading}
              lotId={Number(filters.lotId)}
              otherAvailableRoomOptions={otherAvailableRoomOptions}
            />
          </RoomsSelectionProvider>
        </div>
        <GuideKliaT2 />

        {/* Use mock data for now, later replace with transformSelectedRooms() */}
        <SelectedRoom
          selectedRooms={transformSelectedRooms()}
          onBookNow={() => setClickedBookNow(true)}
        />
      </Box>

      {clickedBookNow && (
        <BookingPayment
          selectedRooms={transformSelectedRooms()}
          hotelDetail={hotelDetail}
          taxAndServiceCharge={taxAndServiceCharge}
          checkInInformation={filters}
          onSubmit={onHandlePayment}
          guestsCountry={guestsCountry || []}
        />
      )}

      <Snackbar
        open={Boolean(errorNotification)}
        anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
        autoHideDuration={4000}
        onClose={handleCloseErrorBooking}
      >
        <Alert
          onClose={handleCloseErrorBooking}
          severity='error'
          variant='filled'
          sx={{ width: '100%', fontSize: '1rem' }}
        >
          {errorNotification}
        </Alert>
      </Snackbar>
      <Footer />
    </Box>
  );
};

export default BookingPageClient;
